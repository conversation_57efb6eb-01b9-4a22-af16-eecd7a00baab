<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Rock Breaker MiniGame</title>
    <style>
      body {
        margin: 0;
        height: 100vh;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background: #f0f0f0;
        font-family: Arial, sans-serif;
        user-select: none;
      }

      #game {
        display: flex;
        flex-direction: column;
        align-items: center;
      }

      #rock {
        width: 150px;
        height: 150px;
        background: gray;
        margin-bottom: 50px;
        transition: transform 0.2s;
      }

      #bar {
        position: relative;
        width: 400px;
        height: 30px;
        background: #ff4444;
        overflow: hidden;
      }

      #hitZone {
        position: absolute;
        top: 0;
        bottom: 0;
        background: #44dd44;
        z-index: 1;
      }

      #cursor {
        position: absolute;
        width: 6px;
        top: 0;
        bottom: 0;
        background: black;
        z-index: 2;
      }

      #stats {
        margin-top: 20px;
        font-size: 18px;
      }
    </style>
  </head>
  <body>
    <div id="game">
      <div id="rock"></div>
      <div id="bar">
        <div id="hitZone"></div>
        <div id="cursor"></div>
      </div>
      <div id="stats">
        <div>Coins: <span id="coins">0</span></div>
        <div>Multiplier: <span id="multiplier">1.0x</span></div>
      </div>
    </div>

    <script>
      const rock = document.getElementById("rock");
      const cursor = document.getElementById("cursor");
      const hitZone = document.getElementById("hitZone");
      const coinsEl = document.getElementById("coins");
      const multiplierEl = document.getElementById("multiplier");
      const bar = document.getElementById("bar");
      const barWidth = bar.offsetWidth;

      let cursorX = 0;
      let cursorDir = 1;
      let cursorSpeed = 4;
      const baseSpeed = 4;

      let prevCursorX = 0;

      let hitZoneWidth = 80;
      const baseZoneWidth = 80;

      let multiplier = 1;
      let coins = 0;
      let rockHp = 100;
      const rockBaseSize = 150;

      // smoother decay
      let decayFactor = 0.98;

      function resetHitZone() {
        hitZoneWidth = baseZoneWidth;
        hitZone.style.width = hitZoneWidth + "px";
        hitZone.style.left = (barWidth - hitZoneWidth) / 2 + "px";
      }

      function moveHitZoneRandom() {
        hitZoneWidth = Math.max(20, hitZoneWidth - 5);
        let randomLeft = Math.random() * (barWidth - hitZoneWidth);
        hitZone.style.width = hitZoneWidth + "px";
        hitZone.style.left = randomLeft + "px";
      }

      resetHitZone();

      // Cursor moves
      function updateCursor() {
        prevCursorX = cursorX;
        cursorX += cursorDir * cursorSpeed;
        if (cursorX <= 0) {
          cursorX = 0;
          cursorDir = 1;
        }
        if (cursorX >= barWidth - cursor.offsetWidth) {
          cursorX = barWidth - cursor.offsetWidth;
          cursorDir = -1;
        }
        cursor.style.left = cursorX + "px";
      }

      function isHit() {
        let hitZoneLeft = hitZone.offsetLeft;
        let hitZoneRight = hitZoneLeft + hitZone.offsetWidth;

        // Check path between prev and current position
        let minX = Math.min(prevCursorX, cursorX);
        let maxX = Math.max(
          prevCursorX + cursor.offsetWidth,
          cursorX + cursor.offsetWidth
        );

        return maxX >= hitZoneLeft && minX <= hitZoneRight;
      }

      document.body.addEventListener("mousedown", () => {
        if (isHit()) {
          // HIT!
          let damage = 5 * multiplier;
          let coinGain = 1 * multiplier;

          rockHp -= damage;
          coins += Math.floor(coinGain);

          multiplier *= 1.1;
          cursorSpeed = baseSpeed * multiplier; // ramp faster
          moveHitZoneRandom();

          if (rockHp <= 0) {
            coins += Math.floor(10 * multiplier); // bonus for breaking rock
            rockHp = 100;
            rock.style.transform = "scale(1)";
          } else {
            let scale = Math.max(0.3, rockHp / 100);
            rock.style.transform = `scale(${scale})`;
          }
        } else {
          // MISS! → half multiplier instead of reset
          multiplier = Math.max(1, multiplier / 2);
          cursorSpeed = baseSpeed;
          resetHitZone();
        }

        multiplierEl.textContent = multiplier.toFixed(1) + "x";
        coinsEl.textContent = coins;
      });

      // Slow decay
      setInterval(() => {
        if (multiplier > 1) {
          multiplier = Math.max(1, multiplier * decayFactor);
          multiplierEl.textContent = multiplier.toFixed(1) + "x";
        }
      }, 1000);

      setInterval(updateCursor, 16);
    </script>
  </body>
</html>