const rock = document.getElementById("rock");
const cursor = document.getElementById("cursor");
const hitZone = document.getElementById("hitZone");
const coinsEl = document.getElementById("coins");
const multiplierEl = document.getElementById("multiplier");
const bar = document.getElementById("bar");
const barWidth = bar.offsetWidth;

let cursorX = 0;
let cursorDir = 1;
let cursorSpeed = 4;
const baseSpeed = 4;

let prevCursorX = 0;

let hitZoneWidth = 80;
const baseZoneWidth = 80;

let multiplier = 1;
let coins = 0;
let rockHp = 100;
const rockBaseSize = 150;

let decayFactor = 0.98;

// --- Load coins from localStorage ---
if (localStorage.getItem("coins")) {
  coins = parseInt(localStorage.getItem("coins"), 10) || 0;
  coinsEl.textContent = coins;
}

function saveCoins() {
  localStorage.setItem("coins", coins);
}

function resetHitZone() {
  hitZoneWidth = baseZoneWidth;
  hitZone.style.width = hitZoneWidth + "px";
  hitZone.style.left = (barWidth - hitZoneWidth) / 2 + "px";
}

function moveHitZoneRandom() {
  hitZoneWidth = Math.max(20, hitZoneWidth - 5);
  let randomLeft = Math.random() * (barWidth - hitZoneWidth);
  hitZone.style.width = hitZoneWidth + "px";
  hitZone.style.left = randomLeft + "px";
}

resetHitZone();

// Cursor moves
function updateCursor() {
  prevCursorX = cursorX;
  cursorX += cursorDir * cursorSpeed;
  if (cursorX <= 0) {
    cursorX = 0;
    cursorDir = 1;
  }
  if (cursorX >= barWidth - cursor.offsetWidth) {
    cursorX = barWidth - cursor.offsetWidth;
    cursorDir = -1;
  }
  cursor.style.left = cursorX + "px";
}

function isHit() {
  let hitZoneLeft = hitZone.offsetLeft;
  let hitZoneRight = hitZoneLeft + hitZone.offsetWidth;

  // Check path between prev and current position
  let minX = Math.min(prevCursorX, cursorX);
  let maxX = Math.max(
    prevCursorX + cursor.offsetWidth,
    cursorX + cursor.offsetWidth
  );

  return maxX >= hitZoneLeft && minX <= hitZoneRight;
}

document.body.addEventListener("click", () => {
  if (isHit()) {
    // HIT!
    let damage = 5 * multiplier;
    let coinGain = 1 * multiplier;

    rockHp -= damage;
    coins += Math.floor(coinGain);

    multiplier *= 1.1;
    cursorSpeed += 1;
    moveHitZoneRandom();

    if (rockHp <= 0) {
      coins += Math.floor(10 * multiplier); // bonus
      rockHp = 100;
      rock.style.transform = "scale(1)";
    } else {
      let scale = Math.max(0.3, rockHp / 100);
      rock.style.transform = `scale(${scale})`;
    }
  } else {
    // MISS → half multiplier
    multiplier = Math.max(1, multiplier / 2);
    cursorSpeed = baseSpeed;
    resetHitZone();
  }

  multiplierEl.textContent = multiplier.toFixed(1) + "x";
  coinsEl.textContent = coins;
  saveCoins(); // persist
});

// Slow decay
setInterval(() => {
  if (multiplier > 1) {
    multiplier = Math.max(1, multiplier * decayFactor);
    multiplierEl.textContent = multiplier.toFixed(1) + "x";
  }
}, 1000);

setInterval(updateCursor, 16);